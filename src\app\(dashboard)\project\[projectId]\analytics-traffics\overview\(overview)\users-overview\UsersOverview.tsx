"use client";
import React, { useState } from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import ChoroplethMap from "./_components/ChoroplethMap";
import DateRange from "../../../_components/date-range/DateRange";
import ProgressBar from "./_components/ProgressBar";
import Badge from "../../../analytic-insight/_components/Badge";
import { Button } from "@/components/ui/button";

/* ================================ API CALLS =============================== */
import useUsersOverview from "./UsersOverview.hook";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import Link from "next/link";

/* ========================================================================== */
const UsersOverview = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const { themeColor } = useAppThemeColor();
  const { isComparisonEnabled } = useDateRangeStore();
  const [selectedItem, setSelectedItem] = useState("Countries");
  const {
    data: useUsersData,
    isLoading: useUsersIsLoading,
    error,
  } = useUsersOverview(selectedItem);

  const badges = ["Countries", "Cities", "Gender", "Device", "Age"];

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  // Show error state if there's an error
  if (error) {
    return (
      <Card className="overflow-hidden space-y-4">
        <Title>Users Overview</Title>
        <DateRange />
        <div className="flex items-center justify-center h-64 text-red-500">
          <p>Error loading users overview data. Please try again.</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden space-y-4">
      <div className="flex items-center justify-between">
        <Title>Users Overview</Title>
        <div className="text-sm text-gray-500">
          Active: {selectedItem}
          {useUsersData && (
            <span className="ml-2 text-green-600">
              ✓ Data Loaded (
              {Object.keys(useUsersData.leftMap).length +
                Object.keys(useUsersData.rightMap).length}{" "}
              countries)
            </span>
          )}
        </div>
      </div>
      <DateRange />

      <div className="flex flex-col lg:flex-row gap-1 lg:items-start mt-4">
        <div className="w-[100%] lg:w-[60%] h-fit grid grid-cols-1 lg:grid-cols-1">
          {useUsersIsLoading && !useUsersData && (
            <>
              <ChoroplethMap
                color="#914AC4"
                countryValues={{ IRN: "0" }}
                className="animate-pulse mb-2"
              />
              <ChoroplethMap
                color="#F8BD00"
                countryValues={{ IRN: "0" }}
                className="animate-pulse"
              />
            </>
          )}
          {useUsersData && (
            <>
              {/* TOP MAP: Current period data (purple) */}
              <ChoroplethMap
                color="#914AC4"
                countryValues={useUsersData?.leftMap || { IRN: "0" }}
                className="mb-2"
              />
              {/* BOTTOM MAP: Comparison period data (yellow) or split data if no comparison */}
              <ChoroplethMap
                color="#F8BD00"
                countryValues={useUsersData?.rightMap || { IRN: "0" }}
              />
            </>
          )}
        </div>
        <div className="w-full lg:w-[42%] mt-1 space-y-4 py-2">
          {/* Badge Section - Always visible, no loading state */}
          <div className="flex justify-between overflow-x-auto gap-2 pb-1">
            {badges.map((badge, index) => (
              <Badge
                onSelect={() => setSelectedItem(badge)}
                key={index}
                selected={selectedItem === badge}
                className={`px-3 py-2.5 text-xs font-medium cursor-pointer transition-all duration-200 whitespace-nowrap flex-1 text-center min-w-0 !w-auto block`}
                style={
                  selectedItem === badge
                    ? {
                        backgroundColor: themeColor + "10",
                        color: themeColor,
                        borderColor: "transparent",
                      }
                    : {}
                }
              >
                {badge}
              </Badge>
            ))}
          </div>

          {/* Progress Bars Section with Enhanced Loading State */}
          {useUsersIsLoading ? (
            // Enhanced loading skeleton with staggered animation
            <div className="space-y-2.5">
              {Array.from({ length: 5 }).map((_, index) => (
                <div
                  key={index}
                  className="space-y-1.5 py-0.5"
                  style={{
                    animationDelay: `${index * 100}ms`,
                  }}
                >
                  {/* Title and percentage skeleton */}
                  <div className="flex justify-between items-center">
                    <div
                      className="h-3 bg-gray-200 rounded animate-pulse"
                      style={{
                        width: `${Math.random() * 50 + 70}px`, // Random width between 70-120px
                        animationDelay: `${index * 150}ms`,
                      }}
                    />
                    <div
                      className="h-3 bg-gray-200 rounded animate-pulse w-8"
                      style={{
                        animationDelay: `${index * 150 + 50}ms`,
                      }}
                    />
                  </div>
                  {/* Progress bar skeleton */}
                  <div className="bg-gray-100 h-3 w-full overflow-hidden rounded-full">
                    <div
                      className="h-full bg-gray-200 rounded-full animate-pulse"
                      style={{
                        width: `${Math.random() * 60 + 20}%`, // Random width between 20-80%
                        animationDelay: `${index * 200}ms`,
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          ) : useUsersData &&
            useUsersData.progressbarData &&
            useUsersData.progressbarData.length > 0 ? (
            // Show actual data when available
            useUsersData.progressbarData.map(({ title, percentage }, index) => (
              <ProgressBar
                key={index}
                isLoading={false}
                percentage={percentage}
                title={title}
                color={index === 0 ? "bg-[#F8BD00]" : ""}
              />
            ))
          ) : (
            // Show no data message only when not loading and no data
            <div className="text-center text-gray-500 py-4">
              No data available for {selectedItem}
            </div>
          )}
        </div>
      </div>
      <div className="w-full flex justify-end py-3 px-3">
        <Link href={"/project/analytics-traffics/analytic-insight?tab=users"}>
          <Button className="justify-self-end" variant={"default"}>
            See All Details
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default UsersOverview;

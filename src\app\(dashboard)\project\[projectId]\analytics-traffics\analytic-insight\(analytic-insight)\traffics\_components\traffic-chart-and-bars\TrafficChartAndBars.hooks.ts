import { useQuery } from "@tanstack/react-query";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import http from "@/services/httpService";
import { processApiErrorEnhanced, logError } from "@/utils/errorHandler";

/* ================================== TYPES ================================= */
import type { ComparisonTrafficOverviewResponse } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/HorizontalBars.types";
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import type { ChartsAndBars } from "../../../types/ChartAndBars.types";

/* ================================== UTILS ================================= */
import { generateTrafficOverviewTabs } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/overview/(overview)/traffic-overview/utils/transformTrafficData";
import { transformTrafficDataToExistingFormat } from "./TrafficChartAndBars.utils";

/* ========================================================================== */
/**
 * Combined hook that provides both chart data (like AudienceOverview)
 * and bar data (like TrafficOverview) for the traffic analytics page
 */
const useTrafficChartAndBars = ({
  tab,
  domesticFilter,
}: {
  tab: string;
  domesticFilter: string;
}) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API calls
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  return useQuery({
    queryKey: [
      "traffic-chart-and-bars",
      projectId,
      startDate,
      endDate,
      comparisonStartDate,
      comparisonEndDate,
      isComparisonEnabled,
      tab,
      domesticFilter,
    ],
    queryFn: async () => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      try {
        // Fetch current period data
        const currentDataPromise = http.get(
          `/api/project/GA4/traffic/overview/${projectId}/`,
          {
            params: {
              start_date: startDate,
              end_date: endDate,
            },
            useAuth: true,
          }
        );

        // Fetch comparison period data if comparison is enabled
        let previousDataPromise = null;
        if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
          previousDataPromise = http.get(
            `/api/project/GA4/traffic/overview/${projectId}/`,
            {
              params: {
                start_date: comparisonStartDate,
                end_date: comparisonEndDate,
              },
              useAuth: true,
            }
          );
        }

        // Execute API calls
        const results = await Promise.allSettled([
          currentDataPromise,
          previousDataPromise,
        ]);

        // Handle current period result
        const currentResult = results[0];
        if (currentResult.status === "rejected") {
          throw new Error(
            `Failed to fetch current period data: ${currentResult.reason}`
          );
        }
        const currentData = currentResult.value.data;

        // Handle comparison period result
        let previousData = null;
        if (previousDataPromise) {
          const previousResult = results[1];
          if (previousResult.status === "fulfilled" && previousResult.value) {
            previousData = previousResult.value.data;
          } else {
            console.warn(
              "Failed to fetch comparison period data:",
              previousResult.status === "rejected"
                ? previousResult.reason
                : "Unknown error"
            );
          }
        }

        // Create comparison response structure
        const comparisonResponse: ComparisonTrafficOverviewResponse = {
          status: currentData.status,
          project_id: currentData.project_id,
          data: {
            current: currentData.data,
            previous: previousData?.data || null,
          },
          last_sync: currentData.last_sync,
        };

        // Generate tabs from API data
        const cardTabs: CardTabType[] = generateTrafficOverviewTabs(
          comparisonResponse.data
        );

        // Transform data to existing ChartsAndBars format
        const transformedData = transformTrafficDataToExistingFormat(
          comparisonResponse.data,
          tab
        );

        // Add cardTabs to the transformed data
        const finalData: ChartsAndBars = {
          ...transformedData,
          cardTabs,
        };

        return finalData;
      } catch (error) {
        const errorResult = processApiErrorEnhanced(error);
        logError(errorResult, "Traffic Chart and Bars");

        // Re-throw with enhanced error information
        const enhancedError = new Error(errorResult.userMessage);
        (enhancedError as any).code = errorResult.code;
        (enhancedError as any).severity = errorResult.severity;
        (enhancedError as any).shouldRetry = errorResult.shouldRetry;
        (enhancedError as any).technicalMessage = errorResult.technicalMessage;

        throw enhancedError;
      }
    },
    enabled: isValidProjectId && !!projectId && !!startDate && !!endDate,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on server errors (5xx) or client errors (4xx)
      const status = (error as any)?.response?.status || (error as any)?.status;
      const errorCode = (error as any)?.code;

      if (status >= 400) {
        console.log(
          `[TrafficChartAndBars] Not retrying due to HTTP ${status} error`
        );
        return false;
      }

      // Don't retry on network errors that are likely persistent
      if (errorCode === "NETWORK_ERROR" || errorCode === "ECONNABORTED") {
        console.log(
          `[TrafficChartAndBars] Not retrying due to ${errorCode} error`
        );
        return false;
      }

      // Only retry unknown errors, max 1 time
      if (failureCount < 1) {
        console.log(
          `[TrafficChartAndBars] Retrying attempt ${failureCount + 1}`
        );
        return true;
      }

      return false;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
  });
};

export default useTrafficChartAndBars;

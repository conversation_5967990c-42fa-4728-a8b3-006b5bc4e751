"use client";
import React, { useEffect, useRef, useState } from "react";
/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "../../../../../_components/date-range/DateRange";
import CardTab from "@/components/ui/card-tab/CardTab";
import LineChartCard from "../../../../overview/_components/LineChartCard";
import OrganicTraffic from "../../../../overview/(overview)/traffic-overview/_components/OrganicTraffic";
import NoData from "../../../../_components/NoData";
import ErrorDisplay from "../../../../_components/ErrorDisplay";
import Dropdown from "@/components/ui/Dropdown";
import useTrafficChartAndBars from "./TrafficChartAndBars.hooks";

/* ============================== FRAMER MOTION ============================= */
import { motion, AnimatePresence } from "framer-motion";

/* ================================= LODASH ================================= */
import isEqual from "lodash.isequal";

/* ================================== TYPES ================================= */
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import type { ChartResponse } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/overview/(overview)/audience-overview/AudienceOverview.types";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";
import SmallChartSkeleton from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/small-chart-skeleton/SmallChartSkeleton";

/* ================================= ZUSTAND ================================ */
import {
  useLineChartDataStore,
  useChartPopupStore,
} from "@/store/useChartPopupStore";

/* ========================================================================== */
const TrafficChartAndBars = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [activeTab, setActiveTab] = useState("Total Users");
  const { themeColor } = useAppThemeColor();
  const domesticInfoFilters = [
    "Total Traffics ",
    "Paid Traffics",
    "Referral Traffics",
    "Social Traffics",
    "Organic Traffics",
  ];
  const [activeDomesticFilter, setActiveDomesticFilter] = useState(
    domesticInfoFilters[0]
  );

  const {
    data: apiData,
    isError,
    error,
    isLoading,
    isPending,
    refetch,
  } = useTrafficChartAndBars({
    tab: activeTab,
    domesticFilter: activeDomesticFilter,
  });

  // Extract data from the new hook structure
  const chartData = apiData?.chartData || [];
  const cardTabs = apiData?.cardTabs || [];
  const barsData = apiData?.barsData;

  const prevCardTabsRef = useRef<CardTabType[] | null>(cardTabs);
  const [cardsDataChanged, setCardsDataChanged] = useState(false);

  // Chart popup functionality (like AudienceOverview)
  const setChartData = useLineChartDataStore((setData) => setData.setChartData);
  const { show } = useChartPopupStore();

  const handleSetChartData = (chart: ChartResponse) => {
    setChartData({
      title: chart.title,
      bigNumber: chart.bigNumber,
      smallNumber: chart.smallNumber,
      data: chart.data,
      hasComparison: chart.hasComparison,
    });
    show();
  };

  useEffect(() => {
    if (cardTabs && !isEqual(prevCardTabsRef.current, cardTabs)) {
      prevCardTabsRef.current = cardTabs;
      setCardsDataChanged(true);
    } else {
      setCardsDataChanged(false);
    }
  }, [cardTabs]);

  useEffect(() => {
    if (cardTabs.length > 0 && !activeTab) {
      setActiveTab(cardTabs[0].title);
    }
  }, [cardTabs, activeTab]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  if (!isError)
    return (
      <Card className="space-y-4">
        <div className="space-y-2">
          <div className="flex w-full justify-between items-center">
            <Title>Traffics</Title>
            <Dropdown>
              <Dropdown.Button>{activeDomesticFilter}</Dropdown.Button>
              <Dropdown.Options>
                {domesticInfoFilters.map((filter, index) => (
                  <Dropdown.Option
                    key={index}
                    onClick={() => setActiveDomesticFilter(filter)}
                  >
                    {filter}
                  </Dropdown.Option>
                ))}
              </Dropdown.Options>
            </Dropdown>
          </div>
          <DateRange />
        </div>
        <div className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap">
          {cardsDataChanged ? (
            <motion.div
              className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
              key={"loading"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
              {Array.from({ length: 7 }).map((_, i) => (
                <CardTab key={i} isLoading />
              ))}
            </motion.div>
          ) : (
            prevCardTabsRef.current && (
              <motion.div
                className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
                key={"data"}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                {prevCardTabsRef.current.map(
                  (
                    {
                      title,
                      changeValue,
                      value,
                    }: { title: string; changeValue: string; value: string },
                    index: number
                  ) => (
                    <CardTab
                      key={index}
                      title={title}
                      value={value}
                      changeValue={changeValue}
                      className={`border-2`}
                      style={
                        activeTab === title
                          ? { borderColor: themeColor }
                          : { borderColor: "transparent" }
                      }
                      onSelect={() => setActiveTab(title)}
                    />
                  )
                )}
              </motion.div>
            )
          )}
        </div>
        <div className="flex flex-col-reverse lg:flex-row items-center lg:items-start gap-y-16 min-h-[310px]">
          <div className=" w-full">
            {isLoading ? (
              <motion.div
                key={"loading"}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <LineChartSkeleton />
              </motion.div>
            ) : (
              data && (
                <motion.div
                  key={"data"}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <GSCLineChart
                    lineChartData={data.lineChartData}
                    colors={data.colors}
                    selectedLines={data.selectedLines}
                    cardsData={data.cardsData}
                  />
                </motion.div>
              )
            )}
          </div>

          <div className="w-full lg:w-[40%] space-y-2">
            {data
              ? data.progressbarData.map(({ title, percentage }, index) => (
                  <ProgressBar
                    key={index}
                    isLoading={false}
                    percentage={percentage}
                    title={title}
                    color={index === 0 ? "bg-[#F8BD00]" : ""}
                  />
                ))
              : !data &&
                Array.from({ length: 7 }).map((_, index) => (
                  <ProgressBar
                    key={index}
                    isLoading={true}
                    percentage={Math.floor(Math.random() * 101)}
                    title={"loading..."}
                    color={index === 0 ? "bg-[#F8BD00]" : ""}
                  />
                ))}
          </div>
        </div>
      </Card>
    );

  if (isError || !data) {
    isError && console.error("fetching chart and bars failed: ", error);
    return <NoData title="About Users" />;
  }
};

export default TrafficChartAndBars;

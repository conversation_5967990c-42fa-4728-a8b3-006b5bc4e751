/* =============================== REACT QUERY ============================== */
import { useQuery } from "@tanstack/react-query";

/* ================================== TYPES ================================= */
import type { ChartResponse } from "./AudienceOverview.types";
import { useProjectId } from "@/hooks/useProjectId";

/* ================================= ZUSTAND ================================ */
import { useDateRangeStore } from "@/store/useDateRangeStore";

/* ================================ HTTP SERVICE ============================= */
import http from "@/services/httpService";

/* ================================ UTILITIES =============================== */
import { transformAudienceOverviewData } from "./AudienceOverview.utils";
import { processApiErrorEnhanced, logError } from "@/utils/errorHandler";

/* ========================================================================== */
export const useAudienceOverview = () => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API calls
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  return useQuery({
    queryKey: [
      "audience-overview",
      projectId,
      startDate,
      endDate,
      comparisonStartDate,
      comparisonEndDate,
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<ChartResponse[]> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      try {
        // Make primary API call
        const primaryResponse = await http.get(
          `/api/project/GA4/audience/overview/${projectId}/`,
          {
            params: {
              start_date: startDate,
              end_date: endDate,
            },
            useAuth: true,
          }
        );

        let comparisonResponse = null;

        // Make comparison API call if comparison is enabled
        if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
          comparisonResponse = await http.get(
            `/api/project/GA4/audience/overview/${projectId}/`,
            {
              params: {
                start_date: comparisonStartDate,
                end_date: comparisonEndDate,
              },
              useAuth: true,
            }
          );
        }

        // Transform data with comparison support
        return transformAudienceOverviewData(
          primaryResponse.data,
          comparisonResponse?.data || null
        );
      } catch (error) {
        const errorResult = processApiErrorEnhanced(error);
        logError(errorResult, "Audience Overview");

        // Re-throw with enhanced error information
        const enhancedError = new Error(errorResult.userMessage);
        (enhancedError as any).code = errorResult.code;
        (enhancedError as any).severity = errorResult.severity;
        (enhancedError as any).shouldRetry = errorResult.shouldRetry;
        (enhancedError as any).technicalMessage = errorResult.technicalMessage;

        throw enhancedError;
      }
    },
    enabled: isValidProjectId && !!projectId && !!startDate && !!endDate,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 5,
    retry: (failureCount, error) => {
      // Don't retry on server errors (5xx) or client errors (4xx)
      const status = (error as any)?.response?.status || (error as any)?.status;
      const errorCode = (error as any)?.code;

      if (status >= 400) {
        console.log(
          `[AudienceOverview] Not retrying due to HTTP ${status} error`
        );
        return false;
      }

      // Don't retry on network errors that are likely persistent
      if (errorCode === "NETWORK_ERROR" || errorCode === "ECONNABORTED") {
        console.log(
          `[AudienceOverview] Not retrying due to ${errorCode} error`
        );
        return false;
      }

      // Only retry unknown errors, max 1 time
      if (failureCount < 1) {
        console.log(`[AudienceOverview] Retrying attempt ${failureCount + 1}`);
        return true;
      }

      return false;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    // Disable background refetching on server errors
    refetchOnWindowFocus: false,
    refetchOnReconnect: (query) => {
      // Only refetch on reconnect if the last error wasn't a server error
      const lastError = query.state.error as any;
      const status = lastError?.response?.status || lastError?.status;
      return status < 500;
    },
  });
};

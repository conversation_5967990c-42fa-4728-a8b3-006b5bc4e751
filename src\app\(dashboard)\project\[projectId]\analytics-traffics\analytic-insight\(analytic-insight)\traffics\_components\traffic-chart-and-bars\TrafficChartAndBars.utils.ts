import type {
  ComparisonTrafficOverviewData,
  TrafficOverviewData,
} from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/HorizontalBars.types";
import type {
  LineChartPoint,
  ColorConfig,
  CardsData,
  ProgressbarData,
} from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import type { ChartsAndBars } from "../../../types/ChartAndBars.types";

/**
 * Formats a number to a readable string with appropriate suffixes
 */
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

/**
 * Calculates growth percentage between current and previous period
 */
export const calculateGrowth = (current: number, previous: number): string => {
  if (previous === 0) return "+0%";
  const growth = ((current - previous) / previous) * 100;
  const sign = growth >= 0 ? "+" : "";
  return `${sign}${growth.toFixed(1)}%`;
};

/**
 * Creates line chart data points in the format expected by GSCLineChart
 */
const createLineChartData = (
  comparisonData: ComparisonTrafficOverviewData
): LineChartPoint[] => {
  // Create a simple 7-day representation for visualization
  // Since traffic overview API doesn't provide daily breakdown, we'll simulate it
  const days = 7;

  return Array.from({ length: days }, (_, index) => {
    const dayName = `Day ${index + 1}`;

    // Simulate clicks and impressions based on total users and page views
    const baseClicks = Math.round(
      (comparisonData.current?.metrics.total_users.total_value || 0) / days
    );
    const baseImpressions = Math.round(
      (comparisonData.current?.metrics.page_views.total_value || 0) / days
    );

    // Add some variation to make the chart more realistic
    const variation = 0.8 + Math.random() * 0.4; // Random between 0.8 and 1.2

    return {
      name: dayName,
      clicks: Math.round(baseClicks * variation),
      impressions: Math.round(baseImpressions * variation),
    };
  });
};

/**
 * Creates color configuration for the chart
 */
const createColorConfig = (): ColorConfig[] => {
  return [
    { name: "clicks", color: "#914AC4" },
    { name: "impressions", color: "#FFCD29" },
  ];
};

/**
 * Creates cards data in the format expected by GSCLineChart
 */
const createCardsData = (
  comparisonData: ComparisonTrafficOverviewData
): CardsData => {
  const currentMetrics = comparisonData.current?.metrics;
  const previousMetrics = comparisonData.previous?.metrics;

  if (!currentMetrics) {
    return {};
  }

  return {
    clicks: {
      amount: currentMetrics.total_users.total_value,
      growth: previousMetrics
        ? calculateGrowth(
            currentMetrics.total_users.total_value,
            previousMetrics.total_users.total_value
          )
        : "+0%",
    },
    impressions: {
      amount: currentMetrics.page_views.total_value,
      growth: previousMetrics
        ? calculateGrowth(
            currentMetrics.page_views.total_value,
            previousMetrics.page_views.total_value
          )
        : "+0%",
    },
  };
};

/**
 * Creates progress bar data from traffic sources
 */
const createProgressBarData = (
  comparisonData: ComparisonTrafficOverviewData,
  activeTab: string
): ProgressbarData[] => {
  const currentData = comparisonData.current;
  if (!currentData) return [];

  // Map tab names to metric keys
  const metricMap: Record<string, keyof typeof currentData.metrics> = {
    "Total Users": "total_users",
    "New Users": "new_users",
    Sessions: "sessions",
    "Active Users": "active_users",
    Views: "page_views",
    "Event Count": "event_count",
    Conversions: "conversions",
  };

  const metricKey = metricMap[activeTab] || "total_users";
  const metric = currentData.metrics[metricKey];
  const totalValue = metric.total_value;

  if (totalValue === 0) return [];

  // Create progress bars for each traffic source
  const trafficSources = [
    { key: "organic", title: "Organic" },
    { key: "paid", title: "Paid" },
    { key: "direct", title: "Direct" },
    { key: "social", title: "Social" },
    { key: "referral", title: "Referral" },
    { key: "email", title: "Email" },
    { key: "unassigned", title: "Unassigned" },
  ];

  return trafficSources
    .map(({ key, title }) => {
      const sourceValue =
        metric.traffic_sources[key as keyof typeof metric.traffic_sources]
          ?.value || 0;
      const percentage = totalValue > 0 ? (sourceValue / totalValue) * 100 : 0;

      return {
        title,
        percentage: Math.round(percentage),
      };
    })
    .filter((item) => item.percentage > 0)
    .sort((a, b) => b.percentage - a.percentage);
};

/**
 * Main transformation function that converts traffic data to the existing ChartsAndBars format
 */
export const transformTrafficDataToExistingFormat = (
  comparisonData: ComparisonTrafficOverviewData,
  activeTab: string
): ChartsAndBars => {
  return {
    cardTabs: [], // Will be populated by generateTrafficOverviewTabs in the hook
    lineChartData: createLineChartData(comparisonData),
    colors: createColorConfig(),
    selectedLines: ["clicks", "impressions"],
    cardsData: createCardsData(comparisonData),
    progressbarData: createProgressBarData(comparisonData, activeTab),
  };
};
